import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LoadingSpinner } from '@/components/custom-ui/loading';
import { useToast } from '@/components/custom-ui/toast';
import { useBusinessContext } from '@/context/BusinessContext';
import type { IAssignment, IAssignmentCategory, IAssignmentCategoryUpdatePayload } from '@/types';
import { Save, DollarSign, Tag } from 'lucide-react';

interface CategoryTabsProps {
  assignmentId: number;
  categoryData?: IAssignmentCategory[];
}

interface CategoryFormData {
  presentValue: number;
  asAnalyzedValue: number;
}

export function CategoryTabs({ assignmentId, categoryData = [] }: CategoryTabsProps) {
  const [formData, setFormData] = useState<Record<number, CategoryFormData>>({});
  const [savingStates, setSavingStates] = useState<Record<number, boolean>>({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('');

  const {
    entities: { categories: contextCategories },
    fetchActions: { fetchEntity, fetchCategories },
    entityActions: { patchEntity },
  } = useBusinessContext();
  const { addToast } = useToast();

  useEffect(() => {
    const loadCategories = async () => {
      setLoading(true);
      try {
        await fetchCategories();
      } catch (error) {
        console.error('Error loading categories:', error);
        addToast({
          type: "error",
          title: "Failed to load categories",
          description: "Could not fetch available categories"
        });
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, [fetchCategories, addToast]);

  useEffect(() => {
    const initialFormData: Record<number, CategoryFormData> = {};

    contextCategories.forEach(category => {
      const existingData = categoryData.find(cd => cd.categoryId === category.id);
      initialFormData[category.id] = {
        presentValue: existingData?.presentValue || 0,
        asAnalyzedValue: existingData?.asAnalyzedValue || 0
      };
    });

    setFormData(initialFormData);
  }, [contextCategories, categoryData]);

  useEffect(() => {
    if (contextCategories.length > 0 && !activeTab) {
      setActiveTab(contextCategories[0].id.toString());
    }
  }, [contextCategories, activeTab]);

  const handleInputChange = (categoryId: number, field: keyof CategoryFormData, value: string) => {
    const numericValue = parseFloat(value) || 0;
    setFormData(prev => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [field]: numericValue
      }
    }));
  };

  const handleSaveCategory = async (categoryId: number) => {
    setSavingStates(prev => ({ ...prev, [categoryId]: true }));

    try {
      const data = formData[categoryId];
      if (!data) {
        throw new Error('No data to save');
      }

      const payload: IAssignmentCategoryUpdatePayload = {
        assignmentID: assignmentId,
        categoryID: categoryId,
        presentValue: data.presentValue,
        asAnalyzedValue: data.asAnalyzedValue
      };

      const response = await patchEntity<IAssignmentCategoryUpdatePayload, IAssignmentCategory>(`/v1/categories/assignment`, payload);
      if (response.error) throw new Error(response.error || 'Failed to save category data');

      addToast({
        type: "success",
        title: "Category updated",
        description: `Successfully updated ${contextCategories.find(c => c.id === categoryId)?.name || 'category'} data`
      });

      updateCategoriesData();
    } catch (error) {
      console.error('Error saving category:', error);
      addToast({
        type: "error",
        title: "Save failed",
        description: error instanceof Error ? error.message : 'Failed to save category data'
      });
    } finally {
      setSavingStates(prev => ({ ...prev, [categoryId]: false }));
    }
  };

  const updateCategoriesData = async () => {

    const updatedAssignment = await fetchEntity<IAssignment>(`/v1/assignments/${assignmentId}`);
    if (updatedAssignment.error || !updatedAssignment.data) {
      console.error('Failed to fetch updated assignment:', updatedAssignment.error);
      addToast({
        type: "error",
        title: "Failed to fetch updated assignment",
        description: updatedAssignment.error || "Failed to fetch updated assignment. Please try again."
      });
      return
    }
    setFormData(updatedAssignment.data.categoryData?.reduce((acc, cd) => {
      acc[cd.categoryId] = {
        presentValue: cd.presentValue,
        asAnalyzedValue: cd.asAnalyzedValue
      };
      return acc;
    }, {} as Record<number, CategoryFormData>) || {});
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (contextCategories.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-muted-foreground">No categories available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <div className="p-2 bg-cyan-100 dark:bg-cyan-900/20 rounded-lg">
            <Tag className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
          </div>
          Assignment Categories
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${contextCategories.length}, minmax(0, 1fr))` }}>
            {contextCategories.map(category => (
              <TabsTrigger
                key={category.id}
                value={category.id.toString()}
                className="flex-1"
              >
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {contextCategories.map(category => {
            const categoryFormData = formData[category.id];
            const isSaving = savingStates[category.id];

            return (
              <TabsContent key={category.id} value={category.id.toString()} className="mt-6">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor={`present-value-${category.id}`} className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Present Value
                      </Label>
                      <Input
                        id={`present-value-${category.id}`}
                        type="number"
                        disabled={isSaving}
                        step="0.01"
                        min="0"
                        value={categoryFormData?.presentValue || 0}
                        onChange={(e) => handleInputChange(category.id, 'presentValue', e.target.value)}
                        placeholder="Enter present value"
                        className="text-right"
                      />
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(categoryFormData?.presentValue || 0)}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`analyzed-value-${category.id}`} className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        As Analyzed Value
                      </Label>
                      <Input
                        id={`analyzed-value-${category.id}`}
                        type="number"
                        disabled={isSaving}
                        step="0.01"
                        min="0"
                        value={categoryFormData?.asAnalyzedValue || 0}
                        onChange={(e) => handleInputChange(category.id, 'asAnalyzedValue', e.target.value)}
                        placeholder="Enter analyzed value"
                        className="text-right"
                      />
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(categoryFormData?.asAnalyzedValue || 0)}
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleSaveCategory(category.id)}
                      disabled={isSaving}
                      className="gap-2"
                    >
                      {isSaving ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      {isSaving ? 'Saving...' : 'Save Category'}
                    </Button>
                  </div>
                </div>
              </TabsContent>
            );
          })}
        </Tabs>
      </CardContent>
    </Card>
  );
}